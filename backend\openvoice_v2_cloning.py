"""
OpenVoice V2 High-Performance Voice Cloning System
Superior voice cloning with 6 languages support and two-stage architecture
"""

import os
import sys
import tempfile
import torch
import torchaudio
import numpy as np
import librosa
import soundfile
from pathlib import Path
import json
import warnings
warnings.filterwarnings("ignore")

# Fix MeCab dictionary path issue BEFORE importing melo
try:
    import unidic_lite
    os.environ['MECAB_DICDIR'] = unidic_lite.DICDIR

    # Monkey patch MeCab to use the correct dictionary
    import MeCab
    original_tagger_init = MeCab.Tagger.__init__

    def patched_tagger_init(self, args=''):
        if not args:
            args = f'-d {unidic_lite.DICDIR}'
        original_tagger_init(self, args)

    MeCab.Tagger.__init__ = patched_tagger_init
    print(f"✅ Set MeCab dictionary path to: {unidic_lite.DICDIR}")
except ImportError:
    print("❌ unidic-lite not found - MeCab may not work properly")

# Add OpenVoice to path
openvoice_path = Path(__file__).parent / "models" / "openvoice" / "OpenVoice"
sys.path.append(str(openvoice_path))

try:
    from openvoice import se_extractor
    from openvoice.api import ToneColorConverter
    from melo.api import TTS
    print("✅ OpenVoice V2 imports successful")
except ImportError as e:
    print(f"❌ OpenVoice V2 import error: {e}")
    print("Please ensure OpenVoice V2 and MeloTTS are properly installed")

class OpenVoiceV2AudioProcessor:
    """Advanced audio processing optimized for OpenVoice V2"""
    
    def __init__(self, target_sr=16000, max_duration=30.0):
        self.target_sr = target_sr  # OpenVoice V2 uses 16kHz
        self.max_duration = max_duration
        self.min_duration = 3.0
        print(f"🎵 OpenVoice V2 Audio Processor initialized (SR: {target_sr}Hz, Max: {max_duration}s)")
    
    def prepare_reference_audio(self, audio_path, enhance=False):
        """Prepare reference audio for OpenVoice V2 voice cloning"""
        print("📊 Processing reference audio for OpenVoice V2...")
        
        # Load audio
        audio, sr = librosa.load(audio_path, sr=None, mono=True)
        original_duration = len(audio) / sr
        
        print(f"📏 Original audio: {original_duration:.1f}s at {sr}Hz")
        
        # Quality checks
        if original_duration < self.min_duration:
            print(f"⚠️ Warning: Audio is {original_duration:.1f}s, recommend at least {self.min_duration}s")
        elif original_duration > self.max_duration:
            print(f"✂️ Trimming audio from {original_duration:.1f}s to {self.max_duration}s")
            audio = self._select_best_segment(audio, sr, self.max_duration)
        
        # Resample to 16kHz for OpenVoice V2
        if sr != self.target_sr:
            print(f"🔄 Resampling from {sr}Hz to {self.target_sr}Hz...")
            audio = librosa.resample(audio, orig_sr=sr, target_sr=self.target_sr)
            sr = self.target_sr
        
        # Optimize for OpenVoice V2
        audio = self._optimize_for_openvoice(audio, sr, enhance)
        
        # Save processed audio
        temp_path = tempfile.mktemp(suffix='.wav')
        soundfile.write(temp_path, audio, sr, subtype='PCM_16')
        
        duration = len(audio) / sr
        print(f"✅ Processed audio ready: {duration:.1f}s")
        return temp_path
    
    def _select_best_segment(self, audio, sr, target_duration):
        """Select the best audio segment for voice cloning"""
        target_samples = int(target_duration * sr)
        if len(audio) <= target_samples:
            return audio
        
        # Find segment with most consistent speech
        hop_length = 512
        energy = librosa.feature.rms(y=audio, hop_length=hop_length)[0]
        
        # Use sliding window to find best segment
        window_frames = int(target_samples / hop_length)
        best_start = 0
        best_score = 0
        
        for i in range(len(energy) - window_frames):
            window_energy = energy[i:i + window_frames]
            score = np.mean(window_energy) * (1.0 / (1.0 + np.var(window_energy)))
            
            if score > best_score:
                best_score = score
                best_start = i
        
        start_sample = best_start * hop_length
        return audio[start_sample:start_sample + target_samples]
    
    def _optimize_for_openvoice(self, audio, sr, enhance=False):
        """Optimize audio specifically for OpenVoice V2"""
        # Normalize
        audio = self._normalize_audio(audio)
        
        # Trim silence
        audio = self._trim_silence(audio, sr)
        
        # Optional enhancement
        if enhance:
            audio = self._enhance_audio(audio, sr)
        
        return audio
    
    def _normalize_audio(self, audio):
        """Smart normalization"""
        max_val = np.max(np.abs(audio))
        if max_val > 0:
            audio = audio * (0.95 / max_val)
        return audio
    
    def _trim_silence(self, audio, sr, threshold_db=30):
        """Trim silence while preserving natural pauses"""
        intervals = librosa.effects.split(audio, top_db=threshold_db)
        if len(intervals) > 0:
            trimmed_audio = []
            for start, end in intervals:
                trimmed_audio.append(audio[start:end])
            return np.concatenate(trimmed_audio) if trimmed_audio else audio
        return audio
    
    def _enhance_audio(self, audio, sr):
        """Optional audio enhancement"""
        # Simple noise reduction using spectral gating
        stft = librosa.stft(audio)
        magnitude = np.abs(stft)
        phase = np.angle(stft)
        
        # Noise gate
        noise_floor = np.percentile(magnitude, 10)
        mask = magnitude > (noise_floor * 2)
        magnitude = magnitude * mask
        
        # Reconstruct
        enhanced_stft = magnitude * np.exp(1j * phase)
        enhanced_audio = librosa.istft(enhanced_stft)
        
        return enhanced_audio


class OpenVoiceV2VoiceCloning:
    """OpenVoice V2 High-Performance Voice Cloning System"""
    
    def __init__(self):
        print("🚀 Initializing OpenVoice V2 Voice Cloning System...")
        
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🔧 Using device: {self.device}")
        
        # Initialize audio processor
        self.audio_processor = OpenVoiceV2AudioProcessor()
        
        # Model paths
        self.models_dir = Path(__file__).parent / "models" / "openvoice"
        self.checkpoints_dir = self.models_dir / "checkpoints_v2"
        
        # Initialize models
        self.tone_color_converter = None
        self.tts_models = {}  # Will store TTS models for different languages
        
        # Voice storage
        self.current_voice_path = None
        self.processed_voice_path = None
        self.reference_speaker_embedding = None
        
        # Supported languages with their codes
        self.supported_languages = {
            'english': 'EN',
            'spanish': 'ES', 
            'french': 'FR',
            'chinese': 'ZH',
            'japanese': 'JP',
            'korean': 'KR'
        }
        
        # Load models
        self._load_models()
        
        print("✅ OpenVoice V2 system initialized!")
    
    def _load_models(self):
        """Load OpenVoice V2 models"""
        print("📦 Loading OpenVoice V2 models...")
        
        try:
            # Load tone color converter (universal model)
            converter_path = self.checkpoints_dir / "converter"
            config_path = converter_path / "config.json"
            checkpoint_path = converter_path / "checkpoint.pth"
            
            if config_path.exists() and checkpoint_path.exists():
                self.tone_color_converter = ToneColorConverter(str(config_path), device=self.device)
                self.tone_color_converter.load_ckpt(str(checkpoint_path))
                print("✅ Tone color converter loaded")
            else:
                print("❌ Tone color converter files not found")
                return False
            
            # Initialize TTS models for supported languages
            for lang_name, lang_code in self.supported_languages.items():
                try:
                    # MeloTTS models are loaded on-demand
                    print(f"✅ {lang_name.title()} TTS ready")
                except Exception as e:
                    print(f"⚠️ Failed to initialize {lang_name} TTS: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading OpenVoice V2 models: {e}")
            return False
    
    def clone_voice(self, audio_file_path, enhance_audio=False):
        """Clone voice using OpenVoice V2"""
        try:
            print(f"\n🎯 Cloning voice with OpenVoice V2...")
            print(f"📁 Input file: {audio_file_path}")
            
            # Process reference audio
            processed_path = self.audio_processor.prepare_reference_audio(
                audio_file_path, enhance=enhance_audio
            )
            
            # Extract speaker embedding
            print("🔍 Extracting speaker embedding...")
            self.reference_speaker_embedding = se_extractor.get_se(
                processed_path, self.tone_color_converter, vad=True
            )
            
            # Store paths
            self.current_voice_path = audio_file_path
            self.processed_voice_path = processed_path
            
            # Get audio info
            audio, sr = librosa.load(processed_path, sr=None)
            duration = len(audio) / sr
            
            voice_info = f"OpenVoice V2 Clone | {duration:.1f}s reference | 6 Languages"
            
            print("✅ Voice cloned successfully with OpenVoice V2!")
            return processed_path, voice_info, "openvoice_v2_sample"
            
        except Exception as e:
            print(f"❌ Voice cloning error: {str(e)}")
            return None, f"❌ Error: {str(e)}", None
    
    def generate_tts(self, text, quality_mode="high", remove_silence=False, 
                     language="english", speed=1.0, advanced_settings=None):
        """Generate TTS with OpenVoice V2 - Two-stage process"""
        try:
            if not self.current_voice_path or self.reference_speaker_embedding is None:
                return None, "❌ Please clone a voice first!"
            
            if not text.strip():
                return None, "❌ Please enter some text!"
            
            print(f"\n⚡ OpenVoice V2 Generation")
            print(f"🎯 Language: {language} | Quality: {quality_mode} | Speed: {speed}x")
            print(f"📝 Text: {text[:100]}{'...' if len(text) > 100 else ''}")
            
            # Stage 1: Generate base TTS with MeloTTS
            base_audio_path = self._generate_base_tts(text, language, speed)
            if not base_audio_path:
                return None, "❌ Base TTS generation failed"
            
            # Stage 2: Apply tone color conversion
            output_path = self._apply_tone_color_conversion(base_audio_path)
            if not output_path:
                return None, "❌ Tone color conversion failed"
            
            # Post-processing
            if remove_silence:
                output_path = self._post_process_audio(output_path, remove_silence)
            
            # Get stats
            duration = librosa.get_duration(filename=output_path)
            word_count = len(text.split())
            
            return output_path, f"⚡ Generated {word_count} words | {duration:.1f}s | OpenVoice V2"
            
        except Exception as e:
            print(f"TTS error: {str(e)}")
            return None, f"❌ Error: {str(e)}"
    
    def _generate_base_tts(self, text, language, speed):
        """Stage 1: Generate base TTS using MeloTTS"""
        print("🎙️ Stage 1: Generating base TTS...")
        
        try:
            # Map language to MeloTTS language code
            lang_code = self.supported_languages.get(language.lower(), 'EN')
            
            # Initialize MeloTTS model for the language
            if lang_code not in self.tts_models:
                self.tts_models[lang_code] = TTS(language=lang_code, device=self.device)
            
            tts_model = self.tts_models[lang_code]
            
            # Generate base audio
            temp_path = tempfile.mktemp(suffix='.wav')
            tts_model.tts_to_file(text, tts_model.hps.data.spk2id['EN-Default'], temp_path, speed=speed)
            
            print("✅ Base TTS generated")
            return temp_path
            
        except Exception as e:
            print(f"❌ Base TTS generation failed: {e}")
            return None
    
    def _apply_tone_color_conversion(self, base_audio_path):
        """Stage 2: Apply tone color conversion"""
        print("🎨 Stage 2: Applying tone color conversion...")
        
        try:
            # Load base audio
            audio, sr = torchaudio.load(base_audio_path)
            
            # Apply tone color conversion
            output_path = tempfile.mktemp(suffix='.wav')
            
            # Convert tone color using the reference speaker embedding
            self.tone_color_converter.convert(
                audio=audio,
                src_se=None,  # Will be extracted from base audio
                tgt_se=self.reference_speaker_embedding,
                output_path=output_path
            )
            
            print("✅ Tone color conversion applied")
            return output_path
            
        except Exception as e:
            print(f"❌ Tone color conversion failed: {e}")
            return None
    
    def _post_process_audio(self, audio_path, remove_silence):
        """Post-process generated audio"""
        if not remove_silence:
            return audio_path
        
        audio, sr = librosa.load(audio_path, sr=None)
        audio = self.audio_processor._trim_silence(audio, sr)
        
        output_path = tempfile.mktemp(suffix='.wav')
        soundfile.write(output_path, audio, sr)
        
        # Clean up original
        if os.path.exists(audio_path):
            os.unlink(audio_path)
        
        return output_path
