"""
StyleTTS2 Model Downloader
Downloads required models for StyleTTS2 voice cloning
"""

import os
import sys
import requests
from pathlib import Path
from huggingface_hub import hf_hub_download, snapshot_download
import torch

def download_with_progress(url, filepath):
    """Download file with progress bar"""
    print(f"📥 Downloading {filepath.name}...")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    downloaded = 0
    
    with open(filepath, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    progress = (downloaded / total_size) * 100
                    print(f"\r📊 Progress: {progress:.1f}%", end='', flush=True)
    
    print(f"\n✅ Downloaded {filepath.name}")

def download_styletts2_models():
    """Download all required StyleTTS2 models"""
    print("🚀 StyleTTS2 Model Downloader")
    print("=" * 50)
    
    # Create models directory
    models_dir = Path("models/styletts2")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        print("\n📦 Step 1: Downloading StyleTTS2 LibriTTS Model...")

        # Download StyleTTS2 LibriTTS checkpoint from correct repo
        styletts2_files = [
            "Models/LibriTTS/epochs_2nd_00012.pth",  # Main checkpoint
            "Models/LibriTTS/config.yml",            # Configuration
        ]

        for filename in styletts2_files:
            try:
                file_path = hf_hub_download(
                    repo_id="yl4579/StyleTTS2-LibriTTS",
                    filename=filename,
                    cache_dir=str(models_dir),
                    local_dir=str(models_dir / "styletts2-libritts"),
                    local_dir_use_symlinks=False
                )
                print(f"✅ Downloaded {filename.split('/')[-1]}")
            except Exception as e:
                print(f"❌ Failed to download {filename}: {e}")
        
        print("\n📦 Step 2: Downloading Multilingual PL-BERT...")
        
        # Download multilingual PL-BERT
        try:
            plbert_dir = models_dir / "multilingual-pl-bert"
            snapshot_download(
                repo_id="papercup-ai/multilingual-pl-bert",
                cache_dir=str(models_dir),
                local_dir=str(plbert_dir),
                local_dir_use_symlinks=False
            )
            print("✅ Downloaded Multilingual PL-BERT")
        except Exception as e:
            print(f"❌ Failed to download PL-BERT: {e}")
        
        print("\n📦 Step 3: Downloading HiFi-GAN Vocoder...")
        
        # Download HiFi-GAN vocoder
        hifigan_files = [
            "g_00500000",  # Generator checkpoint
            "config.json", # Configuration
        ]
        
        for filename in hifigan_files:
            try:
                file_path = hf_hub_download(
                    repo_id="yl4579/StyleTTS2-LibriTTS",
                    filename=f"Models/hifigan/{filename}",
                    cache_dir=str(models_dir),
                    local_dir=str(models_dir / "hifigan"),
                    local_dir_use_symlinks=False
                )
                print(f"✅ Downloaded HiFi-GAN {filename}")
            except Exception as e:
                print(f"❌ Failed to download {filename}: {e}")
        
        print("\n📦 Step 4: Downloading Additional Components...")
        
        # Download additional required files
        additional_files = [
            ("Utils/ASR/config.yml", "asr_config.yml"),
            ("Utils/JDC/bst.t7", "jdc_bst.t7"),
        ]
        
        for remote_path, local_name in additional_files:
            try:
                file_path = hf_hub_download(
                    repo_id="yl4579/StyleTTS2-LibriTTS",
                    filename=remote_path,
                    cache_dir=str(models_dir),
                    local_dir=str(models_dir / "utils"),
                    local_dir_use_symlinks=False
                )
                print(f"✅ Downloaded {local_name}")
            except Exception as e:
                print(f"❌ Failed to download {local_name}: {e}")
        
        print("\n🎉 StyleTTS2 Model Download Complete!")
        print("=" * 50)
        
        # Verify downloads
        print("\n🔍 Verifying downloads...")
        required_files = [
            models_dir / "styletts2-libritts" / "Models" / "LibriTTS" / "epochs_2nd_00012.pth",
            models_dir / "styletts2-libritts" / "Models" / "LibriTTS" / "config.yml",
            models_dir / "multilingual-pl-bert" / "pytorch_model.bin",
            models_dir / "hifigan" / "g_00500000",
        ]
        
        all_present = True
        for file_path in required_files:
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"✅ {file_path.name} ({size_mb:.1f} MB)")
            else:
                print(f"❌ Missing: {file_path.name}")
                all_present = False
        
        if all_present:
            print("\n🎯 All models downloaded successfully!")
            print("🚀 StyleTTS2 is ready to use!")
            
            # Create a marker file
            marker_file = models_dir / "download_complete.txt"
            with open(marker_file, 'w') as f:
                f.write("StyleTTS2 models downloaded successfully\n")
                f.write(f"Download completed at: {torch.cuda.get_device_name() if torch.cuda.is_available() else 'CPU'}\n")
            
            return True
        else:
            print("\n⚠️ Some models are missing. Please run the script again.")
            return False
            
    except Exception as e:
        print(f"\n❌ Download failed: {e}")
        return False

def check_system_requirements():
    """Check if system meets requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"✅ Python {python_version}")

    if sys.version_info < (3, 8, 0):
        print("❌ Python 3.8+ required")
        return False
    
    # Check PyTorch
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
            print(f"✅ VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️ CUDA not available - will use CPU (slower)")
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    # Check disk space
    import shutil
    free_space_gb = shutil.disk_usage('.').free / (1024**3)
    if free_space_gb < 10:
        print(f"⚠️ Low disk space: {free_space_gb:.1f} GB (recommend 10+ GB)")
    else:
        print(f"✅ Disk space: {free_space_gb:.1f} GB available")
    
    return True

if __name__ == "__main__":
    print("🎵 StyleTTS2 Setup for Clonie")
    print("Ultra-fast, human-level voice cloning")
    print("=" * 50)
    
    if not check_system_requirements():
        print("\n❌ System requirements not met")
        sys.exit(1)
    
    if download_styletts2_models():
        print("\n🎉 Setup complete! StyleTTS2 is ready to use.")
        print("🚀 Start your Clonie server to experience 100x faster generation!")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)
