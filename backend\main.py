"""
FastAPI Backend for High-Fidelity Voice Cloning Web App
Converts the original Gradio application to a REST API
"""

import os
import sys
import warnings
import tempfile
import uuid
from pathlib import Path
from typing import Optional

warnings.filterwarnings("ignore")

# Fix MeCab dictionary path issue - use unidic-lite which has working dictionary
try:
    import unidic_lite
    os.environ['MECAB_DICDIR'] = unidic_lite.DICDIR
    print(f"✅ Set MeCab dictionary path to: {unidic_lite.DICDIR}")
except ImportError:
    print("❌ unidic-lite not found - MeCab may not work properly")

# FastAPI imports
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Import the original voice cloning classes
from openvoice_v2_cloning import OpenVoiceV2VoiceCloning

# Initialize FastAPI app
app = FastAPI(
    title="Clonie API",
    description="High-Fidelity Voice Cloning & Text-to-Speech API",
    version="1.0.0"
)

# Add CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # Vite and React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global voice cloning instance
voice_cloning_system: Optional[OpenVoiceV2VoiceCloning] = None

# Pydantic models for API requests
class TTSRequest(BaseModel):
    text: str
    quality_mode: str = "high"
    remove_silence: bool = False

class VoiceCloneResponse(BaseModel):
    success: bool
    message: str
    voice_info: Optional[str] = None
    sample_audio_id: Optional[str] = None

class TTSResponse(BaseModel):
    success: bool
    message: str
    audio_id: Optional[str] = None

# In-memory storage for audio files (in production, use proper file storage)
audio_storage = {}

@app.on_event("startup")
async def startup_event():
    """Initialize the voice cloning system on startup"""
    global voice_cloning_system
    try:
        print("🚀 Initializing High-Fidelity Voice Cloning API...")
        voice_cloning_system = OpenVoiceV2VoiceCloning()
        print("✅ Voice cloning system initialized successfully!")
    except Exception as e:
        print(f"❌ Failed to initialize voice cloning system: {e}")
        raise

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Clonie API is running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "voice_system_ready": voice_cloning_system is not None,
        "message": "High-Fidelity Voice Cloning API"
    }

@app.post("/clone-voice", response_model=VoiceCloneResponse)
async def clone_voice(
    audio_file: UploadFile = File(...),
    enhancement_level: str = Form("none")
):
    """
    Clone a voice from uploaded audio file
    
    Args:
        audio_file: Audio file to clone voice from
        enhancement_level: Processing level ("none", "minimal", "gentle")
    
    Returns:
        VoiceCloneResponse with success status and sample audio
    """
    if not voice_cloning_system:
        raise HTTPException(status_code=500, detail="Voice cloning system not initialized")
    
    if enhancement_level not in ["none", "minimal", "gentle"]:
        raise HTTPException(status_code=400, detail="Invalid enhancement level")
    
    try:
        # Save uploaded file temporarily
        temp_input = tempfile.mktemp(suffix='.wav')
        with open(temp_input, 'wb') as f:
            content = await audio_file.read()
            f.write(content)
        
        # Clone the voice using original logic
        sample_audio_path, voice_info, status_message = voice_cloning_system.clone_voice(
            temp_input, 
            enhancement_level
        )
        
        # Clean up input file
        os.unlink(temp_input)
        
        if sample_audio_path:
            # Store the sample audio with unique ID
            audio_id = str(uuid.uuid4())
            audio_storage[audio_id] = sample_audio_path
            
            return VoiceCloneResponse(
                success=True,
                message=status_message,
                voice_info=voice_info,
                sample_audio_id=audio_id
            )
        else:
            return VoiceCloneResponse(
                success=False,
                message=status_message or "Failed to clone voice"
            )
            
    except Exception as e:
        # Clean up on error
        if 'temp_input' in locals() and os.path.exists(temp_input):
            os.unlink(temp_input)
        
        raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")

@app.post("/generate-tts", response_model=TTSResponse)
async def generate_tts(request: TTSRequest):
    """
    Generate text-to-speech using cloned voice
    
    Args:
        request: TTS request with text and options
    
    Returns:
        TTSResponse with generated audio ID
    """
    if not voice_cloning_system:
        raise HTTPException(status_code=500, detail="Voice cloning system not initialized")
    
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="Text cannot be empty")
    
    if request.quality_mode not in ["high", "standard"]:
        raise HTTPException(status_code=400, detail="Invalid quality mode")
    
    try:
        # Generate TTS using original logic
        output_audio_path, status_message = voice_cloning_system.generate_tts(
            request.text,
            request.quality_mode,
            request.remove_silence
        )
        
        if output_audio_path:
            # Store the generated audio with unique ID
            audio_id = str(uuid.uuid4())
            audio_storage[audio_id] = output_audio_path
            
            return TTSResponse(
                success=True,
                message=status_message,
                audio_id=audio_id
            )
        else:
            return TTSResponse(
                success=False,
                message=status_message or "Failed to generate speech"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

@app.get("/audio/{audio_id}")
async def get_audio(audio_id: str):
    """
    Download audio file by ID
    
    Args:
        audio_id: Unique audio file identifier
    
    Returns:
        Audio file as response
    """
    if audio_id not in audio_storage:
        raise HTTPException(status_code=404, detail="Audio file not found")
    
    audio_path = audio_storage[audio_id]
    
    if not os.path.exists(audio_path):
        # Clean up broken reference
        del audio_storage[audio_id]
        raise HTTPException(status_code=404, detail="Audio file no longer exists")
    
    return FileResponse(
        audio_path,
        media_type="audio/wav",
        filename=f"clonie_audio_{audio_id}.wav"
    )

@app.delete("/audio/{audio_id}")
async def delete_audio(audio_id: str):
    """
    Delete audio file by ID
    
    Args:
        audio_id: Unique audio file identifier
    
    Returns:
        Success message
    """
    if audio_id not in audio_storage:
        raise HTTPException(status_code=404, detail="Audio file not found")
    
    audio_path = audio_storage[audio_id]
    
    # Remove from storage
    del audio_storage[audio_id]
    
    # Delete file if it exists
    if os.path.exists(audio_path):
        os.unlink(audio_path)
    
    return {"message": "Audio file deleted successfully"}

if __name__ == "__main__":
    import uvicorn

    # Get port from environment variable (for Electron) or use default
    port = int(os.environ.get("PORT", 8000))

    print("""
    ╔═══════════════════════════════════════════════════════════════╗
    ║        High-Fidelity Voice Cloning API Server v1.0           ║
    ╠═══════════════════════════════════════════════════════════════╣
    ║   🚀  FastAPI backend for React frontend                      ║
    ║   🎙️  Same high-fidelity voice cloning functionality         ║
    ║   ⚡  RESTful API with CORS support                           ║
    ╚═══════════════════════════════════════════════════════════════╝
    """)

    print(f"🌐 Starting server on port {port}...")

    # Disable reload for PyInstaller compatibility
    reload_enabled = not getattr(sys, 'frozen', False)

    uvicorn.run(
        "main:app" if reload_enabled else app,
        host="0.0.0.0",
        port=port,
        reload=reload_enabled
    )
