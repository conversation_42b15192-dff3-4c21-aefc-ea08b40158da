import React, { useState } from 'react';
import { Settings, RotateCcw, Info } from 'lucide-react';

interface AdvancedTuningProps {
  onSettingsChange: (settings: AdvancedSettings) => void;
  disabled?: boolean;
}

export interface AdvancedSettings {
  temperature: number;
  repetitionPenalty: number;
  lengthPenalty: number;
  topK: number;
  topP: number;
  voiceStability: number;
  emotionStrength: number;
}

const defaultSettings: AdvancedSettings = {
  temperature: 0.75,
  repetitionPenalty: 10.0,
  lengthPenalty: 1.0,
  topK: 50,
  topP: 0.85,
  voiceStability: 0.8,
  emotionStrength: 0.7,
};

const AdvancedTuning: React.FC<AdvancedTuningProps> = ({ onSettingsChange, disabled = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [settings, setSettings] = useState<AdvancedSettings>(defaultSettings);

  const handleSettingChange = (key: keyof AdvancedSettings, value: number) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange(newSettings);
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
    onSettingsChange(defaultSettings);
  };

  const settingsConfig = [
    {
      key: 'temperature' as keyof AdvancedSettings,
      label: 'Creativity',
      description: 'Higher values make speech more varied but less consistent',
      min: 0.1,
      max: 1.5,
      step: 0.05,
      format: (val: number) => val.toFixed(2),
    },
    {
      key: 'voiceStability' as keyof AdvancedSettings,
      label: 'Voice Stability',
      description: 'How closely the generated voice matches the reference',
      min: 0.1,
      max: 1.0,
      step: 0.05,
      format: (val: number) => val.toFixed(2),
    },
    {
      key: 'emotionStrength' as keyof AdvancedSettings,
      label: 'Emotion Strength',
      description: 'How strongly emotions are expressed in the voice',
      min: 0.1,
      max: 1.0,
      step: 0.05,
      format: (val: number) => val.toFixed(2),
    },
    {
      key: 'repetitionPenalty' as keyof AdvancedSettings,
      label: 'Repetition Control',
      description: 'Prevents repetitive speech patterns',
      min: 1.0,
      max: 20.0,
      step: 0.5,
      format: (val: number) => val.toFixed(1),
    },
    {
      key: 'lengthPenalty' as keyof AdvancedSettings,
      label: 'Length Penalty',
      description: 'Controls speech pacing and rhythm',
      min: 0.5,
      max: 2.0,
      step: 0.1,
      format: (val: number) => val.toFixed(1),
    },
    {
      key: 'topK' as keyof AdvancedSettings,
      label: 'Vocabulary Diversity',
      description: 'Number of word choices considered (higher = more diverse)',
      min: 10,
      max: 100,
      step: 5,
      format: (val: number) => val.toString(),
    },
    {
      key: 'topP' as keyof AdvancedSettings,
      label: 'Word Selection',
      description: 'Probability threshold for word selection',
      min: 0.1,
      max: 1.0,
      step: 0.05,
      format: (val: number) => val.toFixed(2),
    },
  ];

  return (
    <div className="space-y-4">
      {/* Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
        disabled={disabled}
      >
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Advanced Voice Tuning</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {isExpanded ? 'Hide' : 'Show'} Controls
          </span>
          <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
            ▼
          </div>
        </div>
      </button>

      {/* Advanced Controls */}
      {isExpanded && (
        <div className="space-y-6 p-4 bg-gray-50 rounded-lg border">
          {/* Info Banner */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-start">
              <Info className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">Advanced Tuning</p>
                <p>Fine-tune these parameters to achieve the perfect voice characteristics. Start with small adjustments and test the results.</p>
              </div>
            </div>
          </div>

          {/* Reset Button */}
          <div className="flex justify-end">
            <button
              onClick={resetToDefaults}
              className="btn-secondary flex items-center text-sm"
              disabled={disabled}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset to Defaults
            </button>
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {settingsConfig.map((config) => (
              <div key={config.key} className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium text-gray-700">
                    {config.label}
                  </label>
                  <span className="text-sm font-mono text-gray-600">
                    {config.format(settings[config.key])}
                  </span>
                </div>
                
                <input
                  type="range"
                  min={config.min}
                  max={config.max}
                  step={config.step}
                  value={settings[config.key]}
                  onChange={(e) => handleSettingChange(config.key, parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  disabled={disabled}
                />
                
                <p className="text-xs text-gray-500">
                  {config.description}
                </p>
              </div>
            ))}
          </div>

          {/* Presets */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700">Quick Presets</h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <button
                onClick={() => {
                  const preset = {
                    ...defaultSettings,
                    voiceStability: 0.9,
                    emotionStrength: 0.5,
                    temperature: 0.6,
                  };
                  setSettings(preset);
                  onSettingsChange(preset);
                }}
                className="btn-secondary text-sm"
                disabled={disabled}
              >
                🎯 Precise
              </button>
              <button
                onClick={() => {
                  const preset = {
                    ...defaultSettings,
                    voiceStability: 0.7,
                    emotionStrength: 0.8,
                    temperature: 0.8,
                  };
                  setSettings(preset);
                  onSettingsChange(preset);
                }}
                className="btn-secondary text-sm"
                disabled={disabled}
              >
                🎭 Expressive
              </button>
              <button
                onClick={() => {
                  const preset = {
                    ...defaultSettings,
                    voiceStability: 0.8,
                    emotionStrength: 0.6,
                    temperature: 0.75,
                  };
                  setSettings(preset);
                  onSettingsChange(preset);
                }}
                className="btn-secondary text-sm"
                disabled={disabled}
              >
                ⚖️ Balanced
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedTuning;
